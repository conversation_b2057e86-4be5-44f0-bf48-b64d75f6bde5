package com.think1024.tocodesign.ideaplugin.toco

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.diff.impl.patch.ApplyPatchStatus
import com.intellij.openapi.diff.impl.patch.PatchReader
import com.intellij.openapi.diff.impl.patch.apply.GenericPatchApplier

object TextFilePatcher {
    private val logger = Logger.getInstance(TextFilePatcher::class.java)

    fun patchText(origText: String, patchText: String): Pair<String?, String?> {
        try {
            // 检查并处理patch格式，移除前两行如果包含---和+++
            val processedPatch = preprocessPatch(patchText)

            val gitDiffPath = "diff --git a/test b/test\n" +
                    "--- a/test\n" +
                    "+++ b/test\n" + processedPatch
            val patchReader = PatchReader(gitDiffPath)
            val patches = patchReader.readTextPatches()
            val patch = patches.firstOrNull()
            if (patch != null) {
                val appliedPatch = GenericPatchApplier.apply(origText, patch.hunks)
                if (appliedPatch?.status == ApplyPatchStatus.SUCCESS) {
                    return Pair(appliedPatch.patchedText, null)
                } else {
                    // 获取patch错误信息
                    try {
                        val patcher = PatchApplier(origText, patch.hunks)
                        patcher.execute()
                        logger.warn("patch apply failed\npatch:\n$processedPatch\norig:\n $origText")
                        return Pair(null, "patch apply failed")
                    } catch (e: Exception) {
                        logger.warn("patch apply failed: ${e.message}\npatch:\n$processedPatch\norig:\n $origText")
                        return Pair(null, e.message)
                    }
                }
            } else {
                logger.warn("patch parse failed\npatch:\n$processedPatch\norig:\n $origText")
                return Pair(null, "patch parse failed")
            }
        } catch (e: Exception) {
            logger.warn("fail to patch text: ${e.message}")
            return Pair(null, e.message)
        }
    }

    private fun preprocessPatch(patch: String): String {
        val lines = patch.lines()
        if (lines.size >= 2) {
            val firstLine = lines[0].trim()
            val secondLine = lines[1].trim()

            // 如果前两行包含---和+++，则移除它们
            if (firstLine.startsWith("---") && secondLine.startsWith("+++")) {
                return lines.drop(2).joinToString("\n")
            }
        }
        return patch
    }
}